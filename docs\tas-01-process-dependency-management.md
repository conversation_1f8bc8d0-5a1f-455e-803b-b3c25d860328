# Dependency Management Guide for IDE AI Agents

## 🛡️ CRITICAL GUARDRAILS

### DO NOT:
- Add dependencies without explicit user approval
- Update major versions automatically
- Install experimental or beta packages
- Add packages with known security vulnerabilities
- Install packages that duplicate existing functionality
- Add packages with bundle size > 100KB without justification
- Install packages with no commits in last 6 months

### ALWAYS:
- Check if functionality exists in current dependencies
- Verify package is actively maintained
- Use exact versions for critical dependencies
- Add to devDependencies if only needed for development
- Check package.json overrides/resolutions for conflicts

## 📋 DEPENDENCY EVALUATION CHECKLIST

### Before Adding New Dependency:
```
- [ ] Does existing dependency already provide this functionality?
- [ ] Is package actively maintained? (commits < 6 months)
- [ ] Download count > 10K weekly on npm?
- [ ] TypeScript support available?
- [ ] Bundle size acceptable? (check bundlephobia.com)
- [ ] Zero critical security vulnerabilities?
- [ ] Compatible with current React/Node version?
```

### Package Quality Assessment:
```
HIGH QUALITY:
├── Weekly downloads > 100K
├── Last commit < 3 months
├── Good TypeScript support
├── Clear documentation
└── Stable version (>1.0.0)

MEDIUM QUALITY:
├── Weekly downloads > 10K
├── Last commit < 6 months
├── Basic TypeScript support
└── Version > 0.5.0

AVOID:
├── Weekly downloads < 1K
├── Last commit > 12 months
├── No TypeScript support
├── Version < 0.3.0
└── Known security issues
```

## 🔧 INSTALLATION PATTERNS

### Safe Installation Commands:
```bash
# Exact version (recommended for critical deps)
npm install react@18.2.0 --save-exact

# Caret range (safe for stable packages)
npm install lodash@^4.17.21

# Dev dependencies
npm install --save-dev @types/node@^20.0.0

# Check before installing
npm view [package-name] versions --json
npm audit [package-name]
```

### Version Range Guidelines:
```json
{
  "dependencies": {
    // Core framework: exact versions
    "react": "18.2.0",
    "typescript": "5.3.3",
    
    // Stable utilities: caret
    "lodash": "^4.17.21",
    "axios": "^1.6.0",
    
    // UI libraries: tilde (patch only)
    "@chakra-ui/react": "~2.8.2"
  }
}
```

## 🚫 FORBIDDEN PACKAGES

### Never Install These:
```
├── left-pad (use native padStart)
├── is-odd, is-even (write simple function)
├── moment (use date-fns or native Date)
├── request (deprecated, use axios/fetch)
└── Any package with "experimental" in name
```

### Preferred Alternatives:
```javascript
// ❌ Don't install
npm install moment

// ✅ Use instead
npm install date-fns
// or native: new Date(), Intl.DateTimeFormat
```

## 📦 DEPENDENCY CATEGORIES

### CORE (Never change without approval):
```
react, react-dom, typescript, next, expo, vue, angular
webpack, vite, babel, eslint, prettier
express, fastify, nest
```

### UTILITIES (Low risk updates):
```
lodash, ramda, date-fns, uuid, validator
axios, fetch, supertest
jest, vitest, testing-library
```

### UI/FEATURES (Medium risk):
```
@mui/material, @chakra-ui, antd
framer-motion, react-spring
react-hook-form, formik
react-query, swr, apollo
```

## 🔄 UPDATE STRATEGIES

### Safe Update Commands:
```bash
# Check outdated packages
npm outdated

# Update patch versions only
npx npm-check-updates --target patch

# Update specific package (minor)
npm install package-name@^2.1.0

# Security updates (always safe)
npm audit fix
```

### Update Priority Order:
```
1. Security patches (immediate)
2. Bug fixes (patch versions)
3. Minor features (minor versions) 
4. Major updates (requires review)
```

## 🧪 TESTING REQUIREMENTS

### After Adding Dependencies:
```bash
# Required checks
npm run build
npm run test
npm run type-check

# Bundle size check
npm run build && npx bundlesize

# Security audit
npm audit --audit-level=moderate
```

### Test New Dependency:
```javascript
// Create test file to verify functionality
// test/dependency-integration.test.js
import { newFunction } from 'new-package';

describe('New Package Integration', () => {
  it('should work with existing code', () => {
    expect(newFunction()).toBeDefined();
  });
});
```

## 📝 DOCUMENTATION REQUIREMENTS

### When Adding Dependency:
```markdown
## Added: package-name@version

**Purpose:** Brief description of why this package was added
**Alternatives considered:** List other options evaluated
**Breaking changes:** None/List any impacts
**Bundle impact:** +X KB (+Y%)
**Migration notes:** Any setup required
```

### Update package.json scripts:
```json
{
  "scripts": {
    "deps:check": "npm outdated",
    "deps:audit": "npm audit",
    "deps:update-patch": "npx ncu -u --target patch",
    "deps:clean": "npm ci"
  }
}
```

## ⚡ COMMON FIXES

### Dependency Conflicts:
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# Force resolutions (package.json)
"overrides": {
  "package-name": "exact-version"
}
```

### TypeScript Issues:
```bash
# Install missing types
npm install --save-dev @types/package-name

# Check TypeScript compatibility
npx tsc --noEmit
```

### Bundle Size Issues:
```bash
# Analyze bundle
npx webpack-bundle-analyzer dist/

# Use tree-shaking imports
import { specific } from 'library/specific'
// instead of: import { specific } from 'library'
```

## 🎯 DECISION MATRIX

### Quick Decision Tree:
```
Can I solve this with existing dependencies? → Use existing
Is this a trivial function? → Write it myself
Is package widely adopted (>100K weekly)? → Consider adding
Is package actively maintained? → Check commit history
Does it increase bundle significantly? → Evaluate alternatives
Is it production-critical? → Use exact version
```

### Standard Responses:
```
"I found that [existing-package] already provides this functionality"
"This can be implemented in 5 lines without additional dependency"
"Package [name] has security vulnerabilities, using [alternative] instead"
"Added [package]@[version] for [specific-purpose], bundle impact: +XKB"
```